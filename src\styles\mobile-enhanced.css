/**
 * Enhanced Mobile Styles
 * Modern mobile-first design with touch optimizations
 */

/* CSS Custom Properties for Mobile */
:root {
  --mobile-header-height: 60px;
  --mobile-tab-height: 50px;
  --mobile-safe-area-top: env(safe-area-inset-top);
  --mobile-safe-area-bottom: env(safe-area-inset-bottom);
  --mobile-safe-area-left: env(safe-area-inset-left);
  --mobile-safe-area-right: env(safe-area-inset-right);
  --mobile-touch-target: 44px;
  --mobile-border-radius: 12px;
  --mobile-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --mobile-shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Mobile Container Base */
.mobile-container {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background: var(--background);
  color: var(--foreground);
}

/* Safe Area Support */
.mobile-safe-top {
  padding-top: var(--mobile-safe-area-top);
}

.mobile-safe-bottom {
  padding-bottom: var(--mobile-safe-area-bottom);
}

.mobile-safe-left {
  padding-left: var(--mobile-safe-area-left);
}

.mobile-safe-right {
  padding-right: var(--mobile-safe-area-right);
}

/* Enhanced Touch Targets */
.touch-target {
  min-height: var(--mobile-touch-target);
  min-width: var(--mobile-touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* Modern Button Styles */
.mobile-button {
  @apply touch-target;
  border-radius: var(--mobile-border-radius);
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.mobile-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 0.2s;
}

.mobile-button:active::before {
  opacity: 0.1;
}

/* Primary Button */
.mobile-button-primary {
  @apply mobile-button;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: var(--mobile-shadow);
}

.mobile-button-primary:hover {
  box-shadow: var(--mobile-shadow-elevated);
  transform: translateY(-1px);
}

.mobile-button-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.mobile-button-secondary {
  @apply mobile-button;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Floating Action Button */
.mobile-fab {
  position: fixed;
  bottom: calc(20px + var(--mobile-safe-area-bottom));
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  box-shadow: var(--mobile-shadow-elevated);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  touch-action: manipulation;
}

.mobile-fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.mobile-fab:active {
  transform: scale(0.95);
}

/* Enhanced Tab Bar */
.mobile-tab-bar {
  height: var(--mobile-tab-height);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  position: relative;
  z-index: 100;
}

.dark .mobile-tab-bar {
  background: rgba(17, 24, 39, 0.95);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.mobile-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 8px;
  transition: all 0.2s;
  position: relative;
  touch-action: manipulation;
}

.mobile-tab-active {
  color: #3b82f6;
}

.mobile-tab-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 3px;
  background: #3b82f6;
  border-radius: 0 0 2px 2px;
}

.mobile-tab-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.2s;
}

.mobile-tab-active .mobile-tab-icon {
  transform: scale(1.1);
}

.mobile-tab-label {
  font-size: 10px;
  font-weight: 500;
  line-height: 1;
}

/* Swipe Gestures */
.swipe-container {
  position: relative;
  overflow: hidden;
  touch-action: pan-x;
}

.swipe-content {
  display: flex;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.swipe-panel {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
}

/* Pull to Refresh */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.pull-indicator.active {
  top: 20px;
}

/* Enhanced Scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

.mobile-scroll::-webkit-scrollbar {
  display: none;
}

/* Modal and Overlay Enhancements */
.mobile-modal {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  padding: var(--mobile-safe-area-bottom) 0 0 0;
}

.mobile-modal-content {
  width: 100%;
  max-height: 90vh;
  background: white;
  border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-modal.open .mobile-modal-content {
  transform: translateY(0);
}

.dark .mobile-modal-content {
  background: #1f2937;
}

/* Sheet Handle */
.mobile-sheet-handle {
  width: 36px;
  height: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  margin: 12px auto 8px;
}

.dark .mobile-sheet-handle {
  background: rgba(255, 255, 255, 0.3);
}

/* Enhanced Input Styles */
.mobile-input {
  width: 100%;
  min-height: var(--mobile-touch-target);
  padding: 12px 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--mobile-border-radius);
  font-size: 16px; /* Prevents zoom on iOS */
  background: white;
  transition: all 0.2s;
}

.mobile-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.dark .mobile-input {
  background: #374151;
  border-color: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Haptic Feedback Simulation */
@keyframes haptic-light {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

@keyframes haptic-medium {
  0% { transform: scale(1); }
  25% { transform: scale(0.96); }
  75% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.haptic-light {
  animation: haptic-light 0.1s ease-out;
}

.haptic-medium {
  animation: haptic-medium 0.15s ease-out;
}

/* Loading States */
.mobile-loading {
  position: relative;
  overflow: hidden;
}

.mobile-loading::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Dark Mode Optimizations */
.dark .mobile-container {
  background: #0f172a;
}

.dark .mobile-button-secondary {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .mobile-button,
  .mobile-tab,
  .swipe-content,
  .mobile-modal-content {
    transition: none;
  }
  
  .mobile-fab:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .mobile-button {
    border: 2px solid currentColor;
  }
  
  .mobile-tab-bar {
    border-top-width: 2px;
  }
}

/* Large Text Support */
@media (prefers-font-size: large) {
  .mobile-tab-label {
    font-size: 12px;
  }
  
  .mobile-input {
    font-size: 18px;
    padding: 16px 20px;
  }
}

/* Landscape Optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-tab-bar {
    height: 40px;
  }
  
  .mobile-tab {
    padding: 4px;
  }
  
  .mobile-tab-icon {
    width: 16px;
    height: 16px;
  }
  
  .mobile-tab-label {
    font-size: 9px;
  }
  
  .mobile-fab {
    width: 48px;
    height: 48px;
    bottom: calc(16px + var(--mobile-safe-area-bottom));
    right: 16px;
  }
}

/* Ultra-wide Mobile Screens */
@media (max-width: 480px) {
  .mobile-button {
    font-size: 14px;
    padding: 10px 16px;
  }
  
  .mobile-input {
    padding: 10px 14px;
  }
  
  .mobile-modal-content {
    max-height: 95vh;
  }
}
