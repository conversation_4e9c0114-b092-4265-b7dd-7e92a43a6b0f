/**
 * Framework7-inspired mobile toolbar component
 */

'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';

interface MobileToolbarProps {
  activeView: 'edit' | 'preview';
}

export function MobileToolbar({ activeView }: MobileToolbarProps) {
  const { state, updateContent } = useApp();
  const [showFormatMenu, setShowFormatMenu] = useState(false);

  const insertText = (before: string, after: string = '') => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const newText = before + selectedText + after;
    
    const newContent = 
      textarea.value.substring(0, start) + 
      newText + 
      textarea.value.substring(end);
    
    updateContent(newContent);
    
    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + before.length + selectedText.length + after.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const formatActions = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      label: 'Undo',
      action: () => {
        document.execCommand('undo');
      }
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      label: 'Redo',
      action: () => {
        document.execCommand('redo');
      }
    },
    {
      icon: <strong>B</strong>,
      label: 'Bold',
      action: () => insertText('**', '**')
    },
    {
      icon: <em>I</em>,
      label: 'Italic',
      action: () => insertText('*', '*')
    },
    {
      icon: <span style={{ textDecoration: 'line-through' }}>S</span>,
      label: 'Strikethrough',
      action: () => insertText('~~', '~~')
    }
  ];

  if (activeView !== 'edit') return null;

  return (
    <>
      {/* Main Toolbar */}
      <div className="bg-gray-100 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 px-2 py-2">
        <div className="flex items-center justify-between">
          {/* Quick Format Buttons */}
          <div className="flex items-center gap-1">
            {formatActions.slice(0, 3).map((action, index) => (
              <button
                key={index}
                onClick={action.action}
                className="p-3 rounded-lg bg-white dark:bg-gray-600 text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center"
                title={action.label}
              >
                {action.icon}
              </button>
            ))}
          </div>

          {/* More Options Button */}
          <button
            onClick={() => setShowFormatMenu(!showFormatMenu)}
            className="p-3 rounded-lg bg-blue-600 text-white shadow-sm hover:bg-blue-700 transition-colors min-w-[44px] min-h-[44px] flex items-center justify-center"
            title="More formatting options"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Extended Format Menu */}
      {showFormatMenu && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-25 z-40"
            onClick={() => setShowFormatMenu(false)}
          />
          
          {/* Menu Panel */}
          <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 rounded-t-xl shadow-2xl z-50 transform transition-transform duration-300">
            {/* Handle */}
            <div className="flex justify-center py-3">
              <div className="w-10 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
            </div>
            
            {/* Menu Content */}
            <div className="px-4 pb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Formatting Options
              </h3>
              
              <div className="grid grid-cols-3 gap-3">
                {[
                  { icon: '📝', label: 'Bold', action: () => insertText('**', '**') },
                  { icon: '📖', label: 'Italic', action: () => insertText('*', '*') },
                  { icon: '🔗', label: 'Link', action: () => insertText('[', '](url)') },
                  { icon: '📋', label: 'Code', action: () => insertText('`', '`') },
                  { icon: '📄', label: 'Quote', action: () => insertText('> ', '') },
                  { icon: '📝', label: 'List', action: () => insertText('- ', '') },
                  { icon: '🔢', label: 'Number', action: () => insertText('1. ', '') },
                  { icon: '📊', label: 'Table', action: () => insertText('| Header |\n|--------|\n| Cell   |', '') },
                  { icon: '💻', label: 'Code Block', action: () => insertText('```\n', '\n```') }
                ].map((item, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      item.action();
                      setShowFormatMenu(false);
                    }}
                    className="flex flex-col items-center gap-2 p-4 rounded-xl bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                  >
                    <span className="text-2xl">{item.icon}</span>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {item.label}
                    </span>
                  </button>
                ))}
              </div>
              
              {/* Close Button */}
              <button
                onClick={() => setShowFormatMenu(false)}
                className="w-full mt-4 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </>
      )}
    </>
  );
}
