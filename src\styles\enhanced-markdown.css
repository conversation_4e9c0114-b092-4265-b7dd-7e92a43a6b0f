/**
 * Enhanced Markdown Styles
 * Comprehensive styling for advanced markdown features
 */

/* Import KaTeX CSS for math rendering */
@import 'katex/dist/katex.min.css';

/* Import Highlight.js CSS for syntax highlighting */
@import 'highlight.js/styles/github.css';

/* Math expressions */
.math-display {
  margin: 1.5rem 0;
  text-align: center;
  overflow-x: auto;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.math-inline {
  display: inline-block;
  margin: 0 0.2rem;
}

.math-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9em;
}

/* Mermaid diagrams */
.mermaid-diagram {
  margin: 2rem 0;
  text-align: center;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
}

.mermaid-rendered svg {
  max-width: 100%;
  height: auto;
}

.diagram-error {
  color: #dc3545;
  background: #f8d7da;
  padding: 1rem;
  border-radius: 4px;
  font-family: monospace;
}

/* Custom containers */
.custom-container {
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-weight: 600;
  gap: 0.5rem;
}

.container-icon {
  font-size: 1.2em;
}

.container-title {
  flex: 1;
}

.container-content {
  padding: 1rem;
}

.custom-container.note {
  border-left: 4px solid #0ea5e9;
}

.custom-container.note .container-header {
  background: #e0f2fe;
  color: #0369a1;
}

.custom-container.note .container-content {
  background: #f0f9ff;
}

.custom-container.warning {
  border-left: 4px solid #f59e0b;
}

.custom-container.warning .container-header {
  background: #fef3c7;
  color: #d97706;
}

.custom-container.warning .container-content {
  background: #fffbeb;
}

.custom-container.info {
  border-left: 4px solid #06b6d4;
}

.custom-container.info .container-header {
  background: #cffafe;
  color: #0891b2;
}

.custom-container.info .container-content {
  background: #f0fdfa;
}

.custom-container.tip {
  border-left: 4px solid #10b981;
}

.custom-container.tip .container-header {
  background: #d1fae5;
  color: #059669;
}

.custom-container.tip .container-content {
  background: #ecfdf5;
}

.custom-container.danger {
  border-left: 4px solid #ef4444;
}

.custom-container.danger .container-header {
  background: #fee2e2;
  color: #dc2626;
}

.custom-container.danger .container-content {
  background: #fef2f2;
}

.custom-container.success {
  border-left: 4px solid #22c55e;
}

.custom-container.success .container-header {
  background: #dcfce7;
  color: #16a34a;
}

.custom-container.success .container-content {
  background: #f0fdf4;
}

/* Enhanced code blocks */
.enhanced-code-block {
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.code-language {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
}

.code-actions {
  display: flex;
  gap: 0.5rem;
}

.copy-code-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-code-btn:hover {
  background: #5a6268;
}

.copy-icon {
  font-size: 0.875em;
}

.code-content {
  margin: 0;
  padding: 1rem;
  overflow-x: auto;
  background: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.code-line {
  display: block;
  position: relative;
  padding-left: 3rem;
}

.code-line::before {
  content: attr(data-line);
  position: absolute;
  left: 0;
  width: 2.5rem;
  text-align: right;
  color: #6c757d;
  font-size: 0.75rem;
  user-select: none;
}

/* Enhanced task lists */
.task-list-checkbox {
  margin-right: 0.5rem;
  cursor: pointer;
}

.task-completed {
  accent-color: #22c55e;
}

.task-pending {
  accent-color: #6b7280;
}

.task-in-progress {
  accent-color: #f59e0b;
}

.task-cancelled {
  accent-color: #ef4444;
}

/* Enhanced tables */
.table-wrapper {
  margin: 1.5rem 0;
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.table-header {
  background: #f8f9fa;
}

.table-header-cell {
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table-cell {
  padding: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.enhanced-table tr:hover {
  background: #f8f9fa;
}

/* Enhanced lists */
.enhanced-list {
  margin: 1rem 0;
  padding-left: 2rem;
}

.list-item {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* Enhanced blockquotes */
.enhanced-blockquote {
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  border-left: 4px solid #6c757d;
  background: #f8f9fa;
  font-style: italic;
  color: #495057;
}

/* Enhanced links */
.enhanced-link {
  color: #0d6efd;
  text-decoration: none;
  transition: color 0.2s;
}

.enhanced-link:hover {
  color: #0a58ca;
  text-decoration: underline;
}

.external-link-icon {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.2rem;
}

/* Enhanced headers */
.enhanced-header {
  position: relative;
  margin: 2rem 0 1rem 0;
  font-weight: 600;
  line-height: 1.2;
}

.header-anchor {
  position: absolute;
  left: -1.5rem;
  opacity: 0;
  color: #6c757d;
  text-decoration: none;
  font-weight: normal;
  transition: opacity 0.2s;
}

.enhanced-header:hover .header-anchor {
  opacity: 1;
}

.header-1 { font-size: 2.5rem; color: #212529; }
.header-2 { font-size: 2rem; color: #343a40; }
.header-3 { font-size: 1.5rem; color: #495057; }
.header-4 { font-size: 1.25rem; color: #6c757d; }
.header-5 { font-size: 1.1rem; color: #6c757d; }
.header-6 { font-size: 1rem; color: #6c757d; }

/* Enhanced paragraphs */
.enhanced-paragraph {
  margin: 1rem 0;
  line-height: 1.6;
  color: #212529;
}

/* Text formatting */
.md-highlight {
  background: #fff3cd;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-inserted {
  background: #d1ecf1;
  text-decoration: none;
  padding: 0.1rem 0.2rem;
  border-radius: 2px;
}

.md-subscript {
  font-size: 0.8em;
}

.md-superscript {
  font-size: 0.8em;
}

.md-kbd {
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 3px;
  padding: 0.1rem 0.3rem;
  font-family: monospace;
  font-size: 0.9em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Progress bars */
.progress-bar {
  position: relative;
  width: 100%;
  height: 1.5rem;
  background: #e9ecef;
  border-radius: 0.75rem;
  overflow: hidden;
  margin: 1rem 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0d6efd, #0a58ca);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Footnotes */
.footnote-ref {
  color: #0d6efd;
  text-decoration: none;
  font-weight: 600;
}

.footnote {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border-left: 3px solid #6c757d;
  font-size: 0.9em;
}

.footnote-backref {
  color: #6c757d;
  text-decoration: none;
  margin-right: 0.5rem;
}

/* Definition lists */
.definition-list {
  margin: 1rem 0;
}

.definition-list dt {
  font-weight: 600;
  margin-top: 1rem;
}

.definition-list dd {
  margin-left: 2rem;
  margin-bottom: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .enhanced-header {
    font-size: 1.5rem;
  }
  
  .header-anchor {
    display: none;
  }
  
  .code-content {
    font-size: 0.8rem;
  }
  
  .table-wrapper {
    font-size: 0.9rem;
  }
  
  .custom-container {
    margin: 1rem 0;
  }
  
  .container-content {
    padding: 0.75rem;
  }
}
