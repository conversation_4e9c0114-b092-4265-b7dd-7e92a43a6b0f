/**
 * Enhanced mobile interface with Framework7-inspired design
 */

'use client';

import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useScrollSync } from '@/hooks/useScrollSync';
import { EditorPane } from '../Editor/EditorPane';
import { PreviewPane } from '../Preview/PreviewPane';

interface MobileInterfaceProps {
  // Props if needed
}

export function MobileInterface({}: MobileInterfaceProps) {
  const { state, updateContent } = useApp();
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const [showToolbar, setShowToolbar] = useState(false);
  const { registerElement } = useScrollSync();

  const insertText = (before: string, after: string = '') => {
    const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const newText = before + selectedText + after;

    const newContent =
      textarea.value.substring(0, start) +
      newText +
      textarea.value.substring(end);

    updateContent(newContent);

    // Set cursor position
    setTimeout(() => {
      textarea.focus();
      const newCursorPos = start + before.length + selectedText.length + after.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  return (
    <div className="h-full flex flex-col bg-white dark:bg-gray-900 relative">
      {/* Enhanced Tab Bar */}
      <div className="relative bg-white dark:bg-gray-800 shadow-sm">
        {/* Tab Buttons */}
        <div className="flex relative">
          <button
            onClick={() => setActiveTab('edit')}
            className={`flex-1 flex items-center justify-center py-4 px-6 transition-all duration-300 relative ${
              activeTab === 'edit'
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-500 dark:text-gray-400'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full transition-all duration-300 ${
                activeTab === 'edit' 
                  ? 'bg-blue-100 dark:bg-blue-900/30' 
                  : 'bg-transparent'
              }`}>
                <svg 
                  className={`w-5 h-5 transition-all duration-300 ${
                    activeTab === 'edit' ? 'scale-110' : 'scale-100'
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={activeTab === 'edit' ? 2.5 : 2} 
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" 
                  />
                </svg>
              </div>
              <span className={`font-medium transition-all duration-300 ${
                activeTab === 'edit' ? 'font-semibold text-lg' : 'text-base'
              }`}>
                Edit
              </span>
            </div>
            
            {/* Active indicator */}
            {activeTab === 'edit' && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-blue-600 dark:bg-blue-400 rounded-full" />
            )}
          </button>

          <button
            onClick={() => setActiveTab('preview')}
            className={`flex-1 flex items-center justify-center py-4 px-6 transition-all duration-300 relative ${
              activeTab === 'preview'
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-gray-500 dark:text-gray-400'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-full transition-all duration-300 ${
                activeTab === 'preview' 
                  ? 'bg-blue-100 dark:bg-blue-900/30' 
                  : 'bg-transparent'
              }`}>
                <svg 
                  className={`w-5 h-5 transition-all duration-300 ${
                    activeTab === 'preview' ? 'scale-110' : 'scale-100'
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={activeTab === 'preview' ? 2.5 : 2} 
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
                  />
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={activeTab === 'preview' ? 2.5 : 2} 
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" 
                  />
                </svg>
              </div>
              <span className={`font-medium transition-all duration-300 ${
                activeTab === 'preview' ? 'font-semibold text-lg' : 'text-base'
              }`}>
                Preview
              </span>
            </div>
            
            {/* Active indicator */}
            {activeTab === 'preview' && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-blue-600 dark:bg-blue-400 rounded-full" />
            )}
          </button>
        </div>

        {/* Background sliding indicator */}
        <div 
          className={`absolute top-0 bottom-0 w-1/2 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/10 dark:to-blue-900/20 transition-transform duration-300 ease-out ${
            activeTab === 'edit' ? 'transform translate-x-0' : 'transform translate-x-full'
          }`}
          style={{ zIndex: 0 }}
        />
      </div>

      {/* Content Area */}
      <div className="flex-1 min-h-0 overflow-hidden relative">
        <div className="h-full">
          {activeTab === 'edit' ? (
            <EditorPane scrollRegister={registerElement} />
          ) : (
            <PreviewPane scrollRegister={registerElement} />
          )}
        </div>
      </div>

      {/* Floating Action Button for Editor Tools */}
      {activeTab === 'edit' && (
        <button
          onClick={() => setShowToolbar(!showToolbar)}
          className={`fixed bottom-6 right-6 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-30 flex items-center justify-center ${
            showToolbar ? 'rotate-45' : 'rotate-0'
          }`}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      )}

      {/* Bottom Toolbar */}
      {showToolbar && activeTab === 'edit' && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-25 z-20"
            onClick={() => setShowToolbar(false)}
          />
          
          {/* Toolbar Panel */}
          <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 rounded-t-2xl shadow-2xl z-40 transform transition-transform duration-300">
            {/* Handle */}
            <div className="flex justify-center py-3">
              <div className="w-12 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full" />
            </div>
            
            {/* Toolbar Content */}
            <div className="px-6 pb-8">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                Formatting Tools
              </h3>
              
              {/* Quick Actions */}
              <div className="grid grid-cols-4 gap-4 mb-6">
                {[
                  { icon: '𝐁', label: 'Bold', action: () => insertText('**', '**') },
                  { icon: '𝐼', label: 'Italic', action: () => insertText('*', '*') },
                  { icon: '🔗', label: 'Link', action: () => insertText('[', '](url)') },
                  { icon: '💻', label: 'Code', action: () => insertText('`', '`') }
                ].map((tool, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      tool.action();
                      setShowToolbar(false);
                    }}
                    className="flex flex-col items-center gap-2 p-4 rounded-2xl bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 active:scale-95"
                  >
                    <span className="text-2xl font-bold">{tool.icon}</span>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {tool.label}
                    </span>
                  </button>
                ))}
              </div>
              
              {/* Extended Actions */}
              <div className="grid grid-cols-3 gap-3 mb-6">
                {[
                  { icon: '📝', label: 'Quote', action: () => insertText('> ', '') },
                  { icon: '📋', label: 'List', action: () => insertText('- ', '') },
                  { icon: '🔢', label: 'Number', action: () => insertText('1. ', '') },
                  { icon: '📊', label: 'Table', action: () => insertText('| Header |\n|--------|\n| Cell   |', '') },
                  { icon: '📷', label: 'Image', action: () => insertText('![alt text](', ')') },
                  { icon: '📄', label: 'Header', action: () => insertText('# ', '') }
                ].map((tool, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      tool.action();
                      setShowToolbar(false);
                    }}
                    className="flex flex-col items-center gap-2 p-3 rounded-xl bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 active:scale-95"
                  >
                    <span className="text-xl">{tool.icon}</span>
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      {tool.label}
                    </span>
                  </button>
                ))}
              </div>
              
              {/* Close Button */}
              <button
                onClick={() => setShowToolbar(false)}
                className="w-full py-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-2xl font-semibold hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
